import React, { useState } from 'react';
import { Tour, Button, FloatButton } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import type { TourProps } from 'antd';

interface HelpTourProps {
  onClose?: () => void;
}

const HelpTour: React.FC<HelpTourProps> = ({ onClose }) => {
  const [open, setOpen] = useState(false);

  const steps: TourProps['steps'] = [
    {
      title: '欢迎使用 VCloud DB 管理界面',
      description: '这是一个数据库管理工具，可以查看和编辑8个数据表的内容。',
      target: null,
    },
    {
      title: '数据表选择',
      description: '点击左侧菜单中的表名来切换不同的数据表。每个表都有不同的数据结构和字段。',
      target: () => document.querySelector('.ant-card') as HTMLElement,
    },
    {
      title: '数据操作工具栏',
      description: '使用工具栏来新增记录、刷新数据或搜索特定内容。',
      target: () => document.querySelector('.ant-card .ant-card-body') as HTMLElement,
    },
    {
      title: '数据表格',
      description: '表格显示所有数据记录。您可以编辑或删除任何记录，也可以使用分页功能浏览大量数据。',
      target: () => document.querySelector('.ant-table') as HTMLElement,
    },
    {
      title: '记录操作',
      description: '每行记录右侧有编辑和删除按钮。编辑会打开表单，删除需要确认。',
      target: () => document.querySelector('.ant-table-tbody tr:first-child td:last-child') as HTMLElement,
    },
    {
      title: '搜索功能',
      description: '使用右上角的搜索框来查找特定记录。搜索会根据不同表的主要字段进行匹配。',
      target: () => document.querySelector('.ant-input-search') as HTMLElement,
    },
  ];

  const handleClose = () => {
    setOpen(false);
    onClose?.();
  };

  return (
    <>
      <FloatButton
        icon={<QuestionCircleOutlined />}
        type="primary"
        style={{ right: 24, bottom: 24 }}
        onClick={() => setOpen(true)}
        tooltip="使用帮助"
      />
      
      <Tour
        open={open}
        onClose={handleClose}
        steps={steps}
        indicatorsRender={(current, total) => (
          <span style={{ color: '#1890ff' }}>
            {current + 1} / {total}
          </span>
        )}
        type="primary"
        arrow={true}
        placement="bottom"
      />
    </>
  );
};

export default HelpTour;
