import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, InputNumber, Switch, DatePicker, Button, Space, message } from 'antd';
import { SaveOutlined, CloseOutlined } from '@ant-design/icons';
import * as TablesTypes from '../../types/tables';
import { contractApi } from '../../services/contractApi';
import dayjs from 'dayjs';

const { TextArea } = Input;

interface DataFormProps {
  tableName: TablesTypes.TableName;
  record?: any; // 编辑时传入的记录，新增时为undefined
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void; // 成功后的回调
}

const DataForm: React.FC<DataFormProps> = ({
  tableName,
  record,
  visible,
  onClose,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const isEdit = !!record;
  const title = isEdit ? `Edit ${tableName}` : `Add ${tableName}`;

  // 当记录变化时，更新表单
  useEffect(() => {
    if (visible) {
      if (isEdit && record) {
        // 编辑模式：填充现有数据
        const formData = { ...record };
        
        // 处理对象字段，转换为JSON字符串
        Object.keys(formData).forEach(key => {
          if (typeof formData[key] === 'object' && formData[key] !== null) {
            if (key === '_id' && formData[key].$oid) {
              formData[key] = formData[key].$oid;
            } else {
              formData[key] = JSON.stringify(formData[key], null, 2);
            }
          }
        });
        
        form.setFieldsValue(formData);
      } else {
        // 新增模式：重置表单
        form.resetFields();
        // 设置默认值
        const now = Math.floor(Date.now() / 1000); // 当前时间戳
        form.setFieldsValue({
          createdAt: now,
          updatedAt: now,
        });
      }
    }
  }, [visible, record, isEdit, form]);

  // 生成表单字段
  const generateFormFields = () => {
    if (!visible) return [];

    // 如果是编辑模式，使用现有记录的字段
    // 如果是新增模式，使用表的基础字段结构
    let sampleRecord = record;
    if (!sampleRecord) {
      // 新增模式：创建基础字段结构
      sampleRecord = {
        _id: '',
        createdAt: 0,
        updatedAt: 0,
        deletedAt: 0,
      };
      
      // 根据表类型添加特定字段
      switch (tableName) {
        case TablesTypes.TABLE_NAMES.USER_SERVICE:
          Object.assign(sampleRecord, {
            duration: 0,
            endAt: 0,
            status: '',
            serviceActivated: false,
            serviceID: '',
            service: '',
            address: '',
            provider: '',
            amount: 0,
          });
          break;
        case TablesTypes.TABLE_NAMES.ORDER:
          Object.assign(sampleRecord, {
            type: '',
            amount: 0,
            amountPaid: 0,
            address: '',
            recipient: '',
            status: '',
          });
          break;
        case TablesTypes.TABLE_NAMES.CLI_VERSION:
          Object.assign(sampleRecord, {
            version: '',
            minimalSupported: '',
            isStable: false,
            downloadUrl: '',
            releaseNotes: '',
          });
          break;
        case TablesTypes.TABLE_NAMES.CURRENCY:
          Object.assign(sampleRecord, {
            nameOrId: '',
            symbolName: '',
            contractId: '',
            decimals: 0,
            totalSupply: 0,
            isActive: false,
          });
          break;
        default:
          Object.assign(sampleRecord, {
            name: '',
            description: '',
          });
          break;
      }
    }

    const fields = Object.keys(sampleRecord);
    
    return fields.map(field => {
      const value = sampleRecord[field];
      
      // 跳过某些系统字段
      if (field === 'deletedAt') {
        return null;
      }

      // ID字段在编辑时只读
      if (field === '_id') {
        return (
          <Form.Item
            key={field}
            name={field}
            label={field}
            rules={[{ required: !isEdit, message: `Please input ${field}!` }]}
          >
            <Input disabled={isEdit} placeholder={`Enter ${field}`} />
          </Form.Item>
        );
      }

      // 时间戳字段 - 简化为数字输入
      if (field.includes('At') || field.includes('TS')) {
        return (
          <Form.Item
            key={field}
            name={field}
            label={`${field} (timestamp)`}
            rules={[{ type: 'number', message: `${field} must be a number!` }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder={`Enter ${field} timestamp (0 for none)`}
              precision={0}
            />
          </Form.Item>
        );
      }

      // 布尔字段
      if (typeof value === 'boolean') {
        return (
          <Form.Item
            key={field}
            name={field}
            label={field}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        );
      }

      // 数字字段
      if (typeof value === 'number') {
        return (
          <Form.Item
            key={field}
            name={field}
            label={field}
            rules={[{ type: 'number', message: `${field} must be a number!` }]}
          >
            <InputNumber 
              style={{ width: '100%' }}
              placeholder={`Enter ${field}`}
              precision={field.includes('amount') ? 4 : 0}
            />
          </Form.Item>
        );
      }

      // 对象字段
      if (typeof value === 'object' && value !== null) {
        return (
          <Form.Item
            key={field}
            name={field}
            label={field}
            rules={[
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve();
                  try {
                    JSON.parse(value);
                    return Promise.resolve();
                  } catch {
                    return Promise.reject(new Error('Invalid JSON format'));
                  }
                }
              }
            ]}
          >
            <TextArea 
              rows={4}
              placeholder={`Enter ${field} as JSON`}
            />
          </Form.Item>
        );
      }

      // 长文本字段
      if (field.includes('description') || field.includes('notes') || field.includes('Note')) {
        return (
          <Form.Item
            key={field}
            name={field}
            label={field}
          >
            <TextArea 
              rows={3}
              placeholder={`Enter ${field}`}
            />
          </Form.Item>
        );
      }

      // 默认字符串字段
      return (
        <Form.Item
          key={field}
          name={field}
          label={field}
          rules={field === 'name' ? [{ required: true, message: `Please input ${field}!` }] : []}
        >
          <Input placeholder={`Enter ${field}`} />
        </Form.Item>
      );
    }).filter(Boolean);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 处理表单数据
      const formData = { ...values };
      
      // 处理JSON字符串字段
      Object.keys(formData).forEach(key => {
        if (typeof formData[key] === 'string' &&
            (key.includes('Options') || key.includes('options') ||
             (formData[key].startsWith('{') && formData[key].endsWith('}')))) {
          try {
            formData[key] = JSON.parse(formData[key]);
          } catch {
            // 如果解析失败，保持原字符串
          }
        }
      });

      if (isEdit) {
        // 更新记录
        await contractApi.updateRecord(tableName, { ids: [record._id] }, formData);
        message.success('Record updated successfully');
      } else {
        // 新增记录
        if (!formData._id) {
          formData._id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        }
        await contractApi.insertRecord(tableName, formData);
        message.success('Record created successfully');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Failed to save record:', error);
      message.error('Failed to save record');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={onClose}>
          <CloseOutlined /> Cancel
        </Button>,
        <Button 
          key="submit" 
          type="primary" 
          loading={loading}
          onClick={handleSubmit}
        >
          <SaveOutlined /> {isEdit ? 'Update' : 'Create'}
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        style={{ maxHeight: '60vh', overflowY: 'auto' }}
      >
        {generateFormFields()}
      </Form>
    </Modal>
  );
};

export default DataForm;
