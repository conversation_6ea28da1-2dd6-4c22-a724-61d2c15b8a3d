/* DataTable 组件样式 */

/* 表格行交替颜色 */
.table-row-light {
  background-color: #fafafa;
}

.table-row-dark {
  background-color: #ffffff;
}

/* 表格行悬停效果 */
.ant-table-tbody > tr:hover > td {
  background-color: #e6f7ff !important;
}

/* 表格头部样式 */
.ant-table-thead > tr > th {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #e8e8e8;
}

/* 操作按钮样式 */
.ant-btn-link {
  padding: 2px 8px;
  height: auto;
  font-size: 12px;
}

/* 搜索框样式 */
.ant-input-search .ant-input-group .ant-input-affix-wrapper {
  border-radius: 6px 0 0 6px;
}

.ant-input-search .ant-input-search-button {
  border-radius: 0 6px 6px 0;
}

/* 分页样式 */
.ant-pagination {
  margin: 16px 0;
  text-align: center;
}

/* 加载状态样式 */
.ant-spin-container {
  min-height: 200px;
}

/* 错误提示样式 */
.ant-alert-error {
  border-radius: 6px;
  border: 1px solid #ff7875;
}

/* 工具栏卡片样式 */
.ant-card-small > .ant-card-body {
  background: linear-gradient(90deg, #fafafa 0%, #f5f5f5 100%);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .ant-table-cell {
    padding: 6px 8px !important;
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .ant-table-scroll {
    overflow-x: auto;
  }

  .ant-input-search {
    width: 100% !important;
    margin-top: 8px;
  }

  .ant-space {
    flex-wrap: wrap;
    gap: 8px !important;
  }

  .ant-btn {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
  }

  .ant-table-pagination {
    text-align: center;
    margin: 16px 8px;
  }

  .ant-pagination-options {
    display: none;
  }
}

@media (max-width: 480px) {
  .ant-table-cell {
    padding: 4px 6px !important;
    font-size: 11px;
  }

  .ant-btn {
    font-size: 11px;
    padding: 2px 6px;
  }

  .ant-input-search .ant-input {
    font-size: 12px;
  }

  .record-count {
    font-size: 11px;
    padding: 2px 8px;
  }
}

/* 表格内容样式优化 */
.ant-table-cell {
  padding: 8px 12px !important;
}

/* 时间戳提示样式 */
.ant-tooltip-inner {
  background-color: #001529;
  border-radius: 4px;
}

/* 记录统计样式 */
.record-count {
  background: linear-gradient(45deg, #1890ff, #52c41a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}
