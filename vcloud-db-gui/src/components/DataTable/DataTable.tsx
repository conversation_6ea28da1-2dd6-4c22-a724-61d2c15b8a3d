import React, { useState, useEffect } from 'react';
import { Table, Button, message, Space, Modal, Input, Tooltip, Alert, Card, Empty, Spin } from 'antd';
import { ReloadOutlined, EditOutlined, DeleteOutlined, PlusOutlined, SearchOutlined, ExclamationCircleOutlined, DatabaseOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import * as TablesTypes from '../../types/tables';
import { contractApi } from '../../services/contractApi';
import DataForm from '../DataForm/DataForm';
import dayjs from 'dayjs';
import './DataTable.css';
import ErrorHandler from '../../utils/errorHandler.tsx';

const { Search } = Input;
const { confirm } = Modal;

// 表名显示映射
const TABLE_DISPLAY_NAMES = {
  [TablesTypes.TABLE_NAMES.USER_SERVICE]: '用户服务',
  [TablesTypes.TABLE_NAMES.ORDER]: '订单',
  [TablesTypes.TABLE_NAMES.ORDER_SERVICE]: '订单服务',
  [TablesTypes.TABLE_NAMES.CLI_VERSION]: 'CLI版本',
  [TablesTypes.TABLE_NAMES.CURRENCY]: '货币',
  [TablesTypes.TABLE_NAMES.SERVICE_CATEGORY]: '服务分类',
  [TablesTypes.TABLE_NAMES.PROVIDER]: '服务提供商',
  [TablesTypes.TABLE_NAMES.SERVICE_TYPE]: '服务类型'
};

interface DataTableProps {
  tableName: TablesTypes.TableName;
}

const DataTable: React.FC<DataTableProps> = ({ tableName }) => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // 表单相关状态
  const [formVisible, setFormVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [searchText, setSearchText] = useState('');

  // 监听窗口大小变化
  React.useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 加载数据
  const loadData = async (searchParams: any = {}) => {
    setLoading(true);
    setError(null);
    try {
      console.log('Loading data for table:', tableName);
      const queryParams = {
        offset: (currentPage - 1) * pageSize,
        limit: pageSize,
        ...searchParams
      };
      const result = await contractApi.findRecords(tableName, queryParams);
      console.log('Loaded data result:', result);
      setData(result.data || []);
      setTotal(result.total || 0);
    } catch (error: any) {
      console.error('Failed to load data:', error);
      const errorInfo = ErrorHandler.handleApiError(error, '数据加载');
      setError(errorInfo.message);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载和表切换时重新加载
  useEffect(() => {
    setCurrent(1);
    loadData();
  }, [tableName]);

  // 分页变化时重新加载
  useEffect(() => {
    loadData();
  }, [currentPage, pageSize]);

  // 刷新数据
  const handleRefresh = () => {
    loadData();
  };

  // 通用文本截断渲染函数
  const renderTruncatedText = (text: any, maxLength: number = 12) => {
    const str = String(text || '');
    return (
      <span title={str} style={{ fontSize: '12px' }}>
        {str.length > maxLength ? `${str.substring(0, maxLength)}...` : str}
      </span>
    );
  };

  // 动态生成所有字段的列配置
  const generateAllFieldsColumns = (): ColumnsType<any> => {
    if (data.length === 0) return [];

    // 获取第一条记录的所有字段
    const firstRecord = data[0];
    const allFields = Object.keys(firstRecord);

    const columns: ColumnsType<any> = allFields.map(field => {
      const column: any = {
        title: field,
        dataIndex: field,
        key: field,
        width: 150,
        ellipsis: true,
      };

      // 特殊字段处理
      if (field === '_id') {
        column.fixed = 'left';
        column.width = 180;
        column.render = (id: any) => {
          let idStr = '';
          if (typeof id === 'object' && id?.$oid) {
            idStr = id.$oid;
          } else {
            idStr = String(id || '');
          }
          return renderTruncatedText(idStr, 12);
        };
      } else if (field === 'createdAt' || field === 'updatedAt' || field.includes('TS') || field.includes('At')) {
        // 时间戳字段 - 显示原始时间戳，tooltip显示人类可读时间
        column.width = 120;
        column.render = (timestamp: number) => {
          if (!timestamp || timestamp <= 0) {
            return '-';
          }

          const humanTime = dayjs.unix(timestamp).format('YYYY-MM-DD HH:mm:ss');
          return (
            <Tooltip title={humanTime}>
              <span style={{ cursor: 'help', textDecoration: 'underline dotted' }}>
                {timestamp}
              </span>
            </Tooltip>
          );
        };
      } else if (typeof firstRecord[field] === 'boolean') {
        // 布尔字段
        column.width = 100;
        column.render = (value: boolean) => String(value);
      } else if (typeof firstRecord[field] === 'number') {
        // 数字字段
        column.width = 120;
        column.render = (value: number) => {
          if (field.includes('amount') || field.includes('Amount')) {
            return value?.toFixed(4) || '0';
          }
          return String(value || 0);
        };
      } else if (typeof firstRecord[field] === 'object' && firstRecord[field] !== null) {
        // 对象字段
        column.width = 200;
        column.render = (obj: any) => (
          <pre style={{ fontSize: '11px', margin: 0, maxHeight: '60px', overflow: 'auto' }}>
            {JSON.stringify(obj, null, 1)}
          </pre>
        );
      } else if (typeof firstRecord[field] === 'string') {
        // 字符串字段
        if (field.includes('address') || field.includes('Address') || field.includes('ID') || field.includes('Id')) {
          column.render = (text: any) => renderTruncatedText(text, 12);
        } else if (field.includes('url') || field.includes('Url') || field.includes('URL')) {
          column.width = 200;
          column.render = (url: any) => {
            const urlStr = String(url || '');
            return (
              <a href={urlStr} target="_blank" rel="noopener noreferrer" title={urlStr}>
                {urlStr.length > 30 ? `${urlStr.substring(0, 30)}...` : urlStr}
              </a>
            );
          };
        }
      }

      return column;
    });

    // 添加操作列
    columns.push({
      title: 'actions',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEdit(record)}
          >
            Edit
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            size="small"
            onClick={() => handleDelete(record)}
          >
            Delete
          </Button>
        </Space>
      ),
    });

    return columns;
  };

  // 生成表格列配置（保留原有的精简版本作为备选）
  const generateColumns = (): ColumnsType<any> => {
    const baseColumns: ColumnsType<any> = [
      {
        title: '_id',
        dataIndex: '_id',
        key: '_id',
        width: 180,
        ellipsis: true,
        fixed: 'left',
        render: (id: any) => {
          // 处理_id可能是对象的情况
          let idStr = '';
          if (typeof id === 'object' && id?.$oid) {
            idStr = id.$oid;
          } else {
            idStr = String(id || '');
          }
          return renderTruncatedText(idStr, 12);
        },
      },
      {
        title: 'createdAt',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 180,
        render: (timestamp: number) =>
          timestamp ? dayjs.unix(timestamp).format('YYYY-MM-DD HH:mm:ss') : '-',
      },
      {
        title: 'updatedAt',
        dataIndex: 'updatedAt',
        key: 'updatedAt',
        width: 180,
        render: (timestamp: number) =>
          timestamp ? dayjs.unix(timestamp).format('YYYY-MM-DD HH:mm:ss') : '-',
      }
    ];

    // 根据表类型添加特定列
    const specificColumns: ColumnsType<any> = [];

    switch (tableName) {
      case TablesTypes.TABLE_NAMES.USER_SERVICE:
        specificColumns.push(
          {
            title: 'serviceID',
            dataIndex: 'serviceID',
            key: 'serviceID',
            width: 150,
            ellipsis: true,
            render: (text: any) => renderTruncatedText(text, 10),
          },
          {
            title: 'service',
            dataIndex: 'service',
            key: 'service',
            width: 120,
            ellipsis: true,
          },
          {
            title: 'address',
            dataIndex: 'address',
            key: 'address',
            width: 150,
            ellipsis: true,
            render: (text: any) => renderTruncatedText(text, 12),
          },
          {
            title: 'status',
            dataIndex: 'status',
            key: 'status',
            width: 100,
          },
          {
            title: 'amount',
            dataIndex: 'amount',
            key: 'amount',
            width: 100,
            render: (amount: number) => amount?.toFixed(2) || '0',
          },
          {
            title: 'serviceActivated',
            dataIndex: 'serviceActivated',
            key: 'serviceActivated',
            width: 120,
            render: (activated: boolean) => String(activated),
          }
        );
        break;

      case TablesTypes.TABLE_NAMES.ORDER:
        specificColumns.push(
          {
            title: 'type',
            dataIndex: 'type',
            key: 'type',
            width: 100,
          },
          {
            title: 'amount',
            dataIndex: 'amount',
            key: 'amount',
            width: 100,
            render: (amount: number) => amount?.toFixed(2) || '0',
          },
          {
            title: 'amountPaid',
            dataIndex: 'amountPaid',
            key: 'amountPaid',
            width: 120,
            render: (amount: number) => amount?.toFixed(2) || '0',
          },
          {
            title: 'address',
            dataIndex: 'address',
            key: 'address',
            width: 150,
            ellipsis: true,
            render: (text: any) => renderTruncatedText(text, 12),
          },
          {
            title: 'recipient',
            dataIndex: 'recipient',
            key: 'recipient',
            width: 150,
            ellipsis: true,
            render: (text: any) => renderTruncatedText(text, 12),
          },
          {
            title: 'status',
            dataIndex: 'status',
            key: 'status',
            width: 100,
          }
        );
        break;

      case TablesTypes.TABLE_NAMES.CLI_VERSION:
        specificColumns.push(
          {
            title: 'version',
            dataIndex: 'version',
            key: 'version',
            width: 120,
          },
          {
            title: 'minimalSupported',
            dataIndex: 'minimalSupported',
            key: 'minimalSupported',
            width: 150,
          },
          {
            title: 'isStable',
            dataIndex: 'isStable',
            key: 'isStable',
            width: 100,
            render: (isStable: boolean) => String(isStable),
          },
          {
            title: 'downloadUrl',
            dataIndex: 'downloadUrl',
            key: 'downloadUrl',
            width: 200,
            ellipsis: true,
            render: (url: any) => {
              const urlStr = String(url || '');
              return (
                <a href={urlStr} target="_blank" rel="noopener noreferrer" title={urlStr}>
                  {urlStr.length > 30 ? `${urlStr.substring(0, 30)}...` : urlStr}
                </a>
              );
            },
          },
          {
            title: 'releaseNotes',
            dataIndex: 'releaseNotes',
            key: 'releaseNotes',
            width: 200,
            ellipsis: true,
          }
        );
        break;

      case TablesTypes.TABLE_NAMES.CURRENCY:
        specificColumns.push(
          {
            title: 'nameOrId',
            dataIndex: 'nameOrId',
            key: 'nameOrId',
            width: 120,
          },
          {
            title: 'symbolName',
            dataIndex: 'symbolName',
            key: 'symbolName',
            width: 100,
          },
          {
            title: 'contractId',
            dataIndex: 'contractId',
            key: 'contractId',
            width: 150,
            ellipsis: true,
            render: (text: any) => renderTruncatedText(text, 12),
          },
          {
            title: 'decimals',
            dataIndex: 'decimals',
            key: 'decimals',
            width: 80,
          },
          {
            title: 'totalSupply',
            dataIndex: 'totalSupply',
            key: 'totalSupply',
            width: 120,
            render: (supply: number) => supply?.toLocaleString() || '0',
          },
          {
            title: 'isActive',
            dataIndex: 'isActive',
            key: 'isActive',
            width: 80,
            render: (isActive: boolean) => String(isActive),
          }
        );
        break;

      case TablesTypes.TABLE_NAMES.ORDER_SERVICE:
        specificColumns.push(
          {
            title: 'orderID',
            dataIndex: 'orderID',
            key: 'orderID',
            width: 150,
            ellipsis: true,
            render: (text: any) => renderTruncatedText(text, 12),
          },
          {
            title: 'userServiceID',
            dataIndex: 'userServiceID',
            key: 'userServiceID',
            width: 150,
            ellipsis: true,
            render: (text: any) => renderTruncatedText(text, 12),
          },
          {
            title: 'orderStatus',
            dataIndex: 'orderStatus',
            key: 'orderStatus',
            width: 120,
          },
          {
            title: 'orderType',
            dataIndex: 'orderType',
            key: 'orderType',
            width: 100,
          }
        );
        break;

      case TablesTypes.TABLE_NAMES.SERVICE_CATEGORY:
        specificColumns.push(
          {
            title: 'name',
            dataIndex: 'name',
            key: 'name',
            width: 150,
          },
          {
            title: 'provider',
            dataIndex: 'provider',
            key: 'provider',
            width: 120,
          },
          {
            title: 'description',
            dataIndex: 'description',
            key: 'description',
            width: 200,
            ellipsis: true,
          },
          {
            title: 'apiHost',
            dataIndex: 'apiHost',
            key: 'apiHost',
            width: 150,
            ellipsis: true,
          }
        );
        break;

      case TablesTypes.TABLE_NAMES.PROVIDER:
        specificColumns.push(
          {
            title: 'name',
            dataIndex: 'name',
            key: 'name',
            width: 120,
          },
          {
            title: 'walletAddress',
            dataIndex: 'walletAddress',
            key: 'walletAddress',
            width: 150,
            ellipsis: true,
            render: (text: any) => renderTruncatedText(text, 12),
          },
          {
            title: 'signAddress',
            dataIndex: 'signAddress',
            key: 'signAddress',
            width: 150,
            ellipsis: true,
            render: (text: any) => renderTruncatedText(text, 12),
          },
          {
            title: 'apiHost',
            dataIndex: 'apiHost',
            key: 'apiHost',
            width: 150,
            ellipsis: true,
          }
        );
        break;

      case TablesTypes.TABLE_NAMES.SERVICE_TYPE:
        specificColumns.push(
          {
            title: 'name',
            dataIndex: 'name',
            key: 'name',
            width: 150,
          },
          {
            title: 'provider',
            dataIndex: 'provider',
            key: 'provider',
            width: 120,
          },
          {
            title: 'category',
            dataIndex: 'category',
            key: 'category',
            width: 120,
          },
          {
            title: 'refundable',
            dataIndex: 'refundable',
            key: 'refundable',
            width: 100,
            render: (refundable: boolean) => String(refundable),
          },
          {
            title: 'description',
            dataIndex: 'description',
            key: 'description',
            width: 200,
            ellipsis: true,
          }
        );
        break;

      default:
        // 对于其他表，显示通用字段
        specificColumns.push(
          {
            title: '数据',
            dataIndex: 'name',
            key: 'name',
            width: 200,
            render: (_, record) => {
              const jsonStr = JSON.stringify(record, null, 1);
              return (
                <pre style={{ fontSize: '11px', margin: 0 }}>
                  {jsonStr.length > 100 ? `${jsonStr.substring(0, 100)}...` : jsonStr}
                </pre>
              );
            },
          }
        );
        break;
    }

    // 操作列
    const actionColumn: ColumnsType<any> = [
      {
        title: 'actions',
        key: 'action',
        width: 150,
        fixed: 'right',
        render: (_, record) => (
          <Space size="small">
            <Button
              type="link"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEdit(record)}
            >
              Edit
            </Button>
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
              onClick={() => handleDelete(record)}
            >
              Delete
            </Button>
          </Space>
        ),
      }
    ];

    return [...baseColumns, ...specificColumns, ...actionColumn];
  };

  // 新增记录
  const handleAdd = () => {
    setEditingRecord(null);
    setFormVisible(true);
  };

  // 编辑记录
  const handleEdit = (record: any) => {
    setEditingRecord(record);
    setFormVisible(true);
  };

  // 删除记录
  const handleDelete = (record: any) => {
    const recordId = typeof record._id === 'object' && record._id?.$oid ? record._id.$oid : record._id;
    const displayId = recordId ? String(recordId).substring(0, 8) + '...' : 'Unknown';

    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>您确定要删除这条记录吗？</p>
          <p><strong>记录ID:</strong> {displayId}</p>
          <p style={{ color: '#ff4d4f', fontSize: '12px' }}>
            ⚠️ 此操作不可撤销，请谨慎操作
          </p>
        </div>
      ),
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 获取正确的ID
          let recordId = record._id;
          if (typeof recordId === 'object' && recordId?.$oid) {
            recordId = recordId.$oid;
          }

          console.log('Deleting record with ID:', recordId, 'from table:', tableName);
          console.log('Full record:', record);

          await contractApi.deleteRecord(tableName, { ids: [recordId] });
          ErrorHandler.showSuccess('记录删除成功');

          // 延迟刷新以确保删除操作完成
          setTimeout(() => {
            loadData();
          }, 100);
        } catch (error: any) {
          ErrorHandler.handleApiError(error, '删除记录');
        }
      },
    });
  };

  // 表单成功回调
  const handleFormSuccess = () => {
    loadData();
  };

  // 关闭表单
  const handleFormClose = () => {
    setFormVisible(false);
    setEditingRecord(null);
  };

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    setCurrent(1);

    if (value.trim()) {
      // 简单的全文搜索实现
      const searchParams: any = {};

      // 根据不同表类型设置搜索字段
      switch (tableName) {
        case TablesTypes.TABLE_NAMES.USER_SERVICE:
          searchParams.address = value;
          break;
        case TablesTypes.TABLE_NAMES.ORDER:
          searchParams.address = value;
          break;
        case TablesTypes.TABLE_NAMES.CLI_VERSION:
          searchParams.version = value;
          break;
        case TablesTypes.TABLE_NAMES.CURRENCY:
          searchParams.symbolName = value;
          break;
        case TablesTypes.TABLE_NAMES.SERVICE_CATEGORY:
        case TablesTypes.TABLE_NAMES.PROVIDER:
        case TablesTypes.TABLE_NAMES.SERVICE_TYPE:
          searchParams.name = value;
          break;
        default:
          break;
      }

      loadData(searchParams);
    } else {
      loadData();
    }
  };

  // 表格变化处理
  const handleTableChange = (pagination: any) => {
    if (pagination) {
      setCurrent(pagination.current);
      setPageSize(pagination.pageSize);
    }
  };

  return (
    <div>
      {/* 工具栏 */}
      <Card
        size="small"
        style={{ marginBottom: 16 }}
        bodyStyle={{ padding: isMobile ? '8px 12px' : '12px 16px' }}
      >
        <div style={{
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          justifyContent: 'space-between',
          alignItems: isMobile ? 'stretch' : 'center',
          gap: isMobile ? '12px' : '0'
        }}>
          <Space size={isMobile ? "small" : "middle"} wrap>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
              size={isMobile ? "small" : "middle"}
            >
              新增记录
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
              size={isMobile ? "small" : "middle"}
            >
              刷新数据
            </Button>
            <div style={{
              padding: isMobile ? '2px 8px' : '4px 12px',
              background: '#f0f0f0',
              borderRadius: '4px',
              fontSize: isMobile ? '11px' : '12px',
              color: '#666'
            }}>
              共 <strong style={{ color: '#1890ff' }}>{total}</strong> 条记录
            </div>
          </Space>

          <div style={{ width: isMobile ? '100%' : 'auto' }}>
            <Search
              placeholder={`搜索 ${TABLE_DISPLAY_NAMES[tableName] || tableName}...`}
              allowClear
              enterButton={<SearchOutlined />}
              size={isMobile ? "small" : "middle"}
              onSearch={handleSearch}
              style={{ width: isMobile ? '100%' : 320 }}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </div>
        </div>
      </Card>

      {/* 错误显示 */}
      {error && (
        <Alert
          message="数据加载失败"
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
          style={{ marginBottom: 16 }}
          action={
            <Button size="small" danger onClick={handleRefresh}>
              重试
            </Button>
          }
        />
      )}

      {/* 数据表格 */}
      <Card
        size="small"
        style={{
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          borderRadius: '6px'
        }}
        bodyStyle={{ padding: 0 }}
      >
        <Table
          columns={generateAllFieldsColumns()}
          dataSource={data}
          rowKey={(record, index) => {
            // 处理_id可能是对象的情况
            if (typeof record._id === 'object' && record._id?.$oid) {
              return record._id.$oid;
            }
            if (record._id) {
              return String(record._id);
            }
            // 如果没有_id，使用索引作为key
            return `row-${index}`;
          }}
          loading={loading}
          locale={{
            emptyText: (
              <Empty
                image={<DatabaseOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />}
                description={
                  <div>
                    <p style={{ color: '#999', margin: '8px 0' }}>
                      {searchText ? '没有找到匹配的记录' : '暂无数据'}
                    </p>
                    {searchText && (
                      <Button
                        type="link"
                        size="small"
                        onClick={() => {
                          setSearchText('');
                          loadData();
                        }}
                      >
                        清除搜索条件
                      </Button>
                    )}
                  </div>
                }
              />
            )
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100'],
            style: { padding: '16px' }
          }}
          scroll={{
            x: 'max-content',  // 自适应内容宽度
            y: 'calc(100vh - 400px)'  // 动态高度，根据视窗调整
          }}
          onChange={handleTableChange}
          size="small"  // 使用小尺寸以节省空间
          bordered={false}  // 移除边框，使用Card的边框
          rowClassName={(record, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
        />
      </Card>

      {/* 数据编辑表单 */}
      <DataForm
        tableName={tableName}
        record={editingRecord}
        visible={formVisible}
        onClose={handleFormClose}
        onSuccess={handleFormSuccess}
      />
    </div>
  );
};

export default DataTable;
