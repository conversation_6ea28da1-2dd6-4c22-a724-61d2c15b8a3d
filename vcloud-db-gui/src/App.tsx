import React, { useState } from 'react';
import { Layout, Menu, Typography, theme } from 'antd';
import { DatabaseOutlined, TableOutlined } from '@ant-design/icons';
import * as TablesTypes from './types/tables';
import DataTable from './components/DataTable/DataTable';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

// 表名显示映射
const TABLE_DISPLAY_NAMES = {
  [TablesTypes.TABLE_NAMES.USER_SERVICE]: '用户服务',
  [TablesTypes.TABLE_NAMES.ORDER]: '订单',
  [TablesTypes.TABLE_NAMES.ORDER_SERVICE]: '订单服务',
  [TablesTypes.TABLE_NAMES.CLI_VERSION]: 'CLI版本',
  [TablesTypes.TABLE_NAMES.CURRENCY]: '货币',
  [TablesTypes.TABLE_NAMES.SERVICE_CATEGORY]: '服务分类',
  [TablesTypes.TABLE_NAMES.PROVIDER]: '服务提供商',
  [TablesTypes.TABLE_NAMES.SERVICE_TYPE]: '服务类型'
};

function App() {
  const [selectedTable, setSelectedTable] = useState<TablesTypes.TableName>(TablesTypes.TABLE_NAMES.USER_SERVICE);
  const [collapsed, setCollapsed] = useState(false);

  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  // 生成菜单项
  const menuItems = Object.entries(TablesTypes.TABLE_NAMES).map(([key, value]) => ({
    key: value,
    icon: <TableOutlined />,
    label: TABLE_DISPLAY_NAMES[value as TablesTypes.TableName],
  }));

  const handleMenuClick = ({ key }: { key: string }) => {
    setSelectedTable(key as TablesTypes.TableName);
  };

  return (
    <div style={{ display: 'flex', minHeight: '100vh' }}>
      {/* 左侧菜单 */}
      <div style={{
        width: '250px',
        background: '#f5f5f5',
        padding: '20px',
        borderRight: '1px solid #ddd'
      }}>
        <h2 style={{ color: '#1890ff', marginBottom: '20px' }}>
          <DatabaseOutlined /> VCloud DB
        </h2>

        <div>
          {Object.entries(TablesTypes.TABLE_NAMES).map(([key, value]) => (
            <div
              key={value}
              onClick={() => setSelectedTable(value as TablesTypes.TableName)}
              style={{
                padding: '10px',
                margin: '5px 0',
                background: selectedTable === value ? '#1890ff' : '#fff',
                color: selectedTable === value ? '#fff' : '#000',
                cursor: 'pointer',
                borderRadius: '4px',
                border: '1px solid #ddd'
              }}
            >
              <TableOutlined /> {TABLE_DISPLAY_NAMES[value as TablesTypes.TableName]}
            </div>
          ))}
        </div>
      </div>

      {/* 右侧内容 */}
      <div style={{ flex: 1, padding: '20px' }}>
        <h1>当前表: {TABLE_DISPLAY_NAMES[selectedTable]}</h1>
        <div style={{ border: '2px solid red', padding: '10px', margin: '10px 0' }}>
          <h3>DataTable容器 (红色边框)</h3>
          <DataTable tableName={selectedTable} />
        </div>
      </div>
    </div>
  );
}

export default App;
