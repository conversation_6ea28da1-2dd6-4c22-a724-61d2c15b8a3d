import React, { useState, useEffect } from 'react';
import { Layout, Menu, Typography, theme, Card, Badge, Divider, Drawer, But<PERSON> } from 'antd';
import { DatabaseOutlined, TableOutlined, InfoCircleOutlined, MenuOutlined } from '@ant-design/icons';
import * as TablesTypes from './types/tables';
import DataTable from './components/DataTable/DataTable';
import HelpTour from './components/HelpTour/HelpTour';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

// 表名显示映射
const TABLE_DISPLAY_NAMES = {
  [TablesTypes.TABLE_NAMES.USER_SERVICE]: '用户服务',
  [TablesTypes.TABLE_NAMES.ORDER]: '订单',
  [TablesTypes.TABLE_NAMES.ORDER_SERVICE]: '订单服务',
  [TablesTypes.TABLE_NAMES.CLI_VERSION]: 'CLI版本',
  [TablesTypes.TABLE_NAMES.CURRENCY]: '货币',
  [TablesTypes.TABLE_NAMES.SERVICE_CATEGORY]: '服务分类',
  [TablesTypes.TABLE_NAMES.PROVIDER]: '服务提供商',
  [TablesTypes.TABLE_NAMES.SERVICE_TYPE]: '服务类型'
};

function App() {
  const [selectedTable, setSelectedTable] = useState<TablesTypes.TableName>(TablesTypes.TABLE_NAMES.USER_SERVICE);
  const [collapsed, setCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [drawerVisible, setDrawerVisible] = useState(false);

  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (!mobile) {
        setDrawerVisible(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 生成菜单项
  const menuItems = Object.entries(TablesTypes.TABLE_NAMES).map(([key, value]) => ({
    key: value,
    icon: <TableOutlined />,
    label: TABLE_DISPLAY_NAMES[value as TablesTypes.TableName],
  }));

  const handleMenuClick = ({ key }: { key: string }) => {
    setSelectedTable(key as TablesTypes.TableName);
    if (isMobile) {
      setDrawerVisible(false);
    }
  };

  // 侧边栏内容组件
  const SidebarContent = () => (
    <div>
      <div style={{ marginBottom: '32px', textAlign: 'center' }}>
        <h2 style={{
          color: '#1890ff',
          marginBottom: '8px',
          fontSize: '20px',
          fontWeight: 'bold'
        }}>
          <DatabaseOutlined style={{ marginRight: '8px' }} />
          VCloud DB
        </h2>
        <p style={{
          color: '#666',
          fontSize: '12px',
          margin: 0
        }}>
          数据库管理界面
        </p>
      </div>

      <Divider orientation="left" style={{ fontSize: '12px', color: '#999' }}>
        数据表 ({Object.keys(TablesTypes.TABLE_NAMES).length})
      </Divider>

      <div>
        {Object.entries(TablesTypes.TABLE_NAMES).map(([key, value]) => (
          <Card
            key={value}
            size="small"
            hoverable
            onClick={() => handleMenuClick({ key: value })}
            style={{
              marginBottom: '8px',
              cursor: 'pointer',
              border: selectedTable === value ? '2px solid #1890ff' : '1px solid #e8e8e8',
              background: selectedTable === value ? '#e6f7ff' : '#fff',
              transition: 'all 0.3s ease'
            }}
            bodyStyle={{ padding: '12px 16px' }}
          >
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <span style={{
                color: selectedTable === value ? '#1890ff' : '#333',
                fontWeight: selectedTable === value ? 'bold' : 'normal',
                fontSize: '13px'
              }}>
                <TableOutlined style={{ marginRight: '8px' }} />
                {TABLE_DISPLAY_NAMES[value as TablesTypes.TableName]}
              </span>
              {selectedTable === value && (
                <Badge status="processing" />
              )}
            </div>
          </Card>
        ))}
      </div>

      <Divider />
      <div style={{ fontSize: '11px', color: '#999', textAlign: 'center' }}>
        <InfoCircleOutlined style={{ marginRight: '4px' }} />
        Mock数据模式
      </div>
    </div>
  );

  return (
    <div style={{ display: 'flex', minHeight: '100vh', background: '#f0f2f5' }}>
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <div style={{
          width: '280px',
          background: '#fff',
          padding: '24px',
          borderRight: '1px solid #e8e8e8',
          boxShadow: '2px 0 8px rgba(0,0,0,0.06)'
        }}>
          <SidebarContent />
        </div>
      )}

      {/* 移动端抽屉 */}
      <Drawer
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <DatabaseOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
            <span>VCloud DB</span>
          </div>
        }
        placement="left"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        width={280}
        bodyStyle={{ padding: '16px' }}
      >
        <SidebarContent />
      </Drawer>

      {/* 右侧内容 */}
      <div style={{ flex: 1, padding: isMobile ? '12px' : '24px' }}>
        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {isMobile && (
                  <Button
                    type="text"
                    icon={<MenuOutlined />}
                    onClick={() => setDrawerVisible(true)}
                    style={{ marginRight: '12px' }}
                  />
                )}
                <TableOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                <span style={{ fontSize: isMobile ? '14px' : '16px' }}>
                  {TABLE_DISPLAY_NAMES[selectedTable]}
                </span>
                <Badge
                  count="数据表"
                  style={{
                    backgroundColor: '#52c41a',
                    marginLeft: '12px',
                    fontSize: '10px'
                  }}
                />
              </div>
              {!isMobile && (
                <div style={{ fontSize: '12px', color: '#666' }}>
                  表名: {selectedTable}
                </div>
              )}
            </div>
          }
          style={{
            minHeight: isMobile ? 'calc(100vh - 24px)' : 'calc(100vh - 48px)',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
          }}
          bodyStyle={{ padding: isMobile ? '12px' : '24px' }}
        >
          <DataTable tableName={selectedTable} />
        </Card>
      </div>

      {/* 帮助引导 */}
      <HelpTour />
    </div>
  );
}

export default App;
