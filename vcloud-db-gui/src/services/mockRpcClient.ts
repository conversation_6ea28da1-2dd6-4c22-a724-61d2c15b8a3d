// Mock RPC 客户端实现
import * as RPCTypes from '../types/rpc';
import * as TablesTypes from '../types/tables';

// Mock数据文件映射
const MOCK_DATA_FILES = {
  [TablesTypes.TABLE_NAMES.USER_SERVICE]: 'userservice-data.ts',
  [TablesTypes.TABLE_NAMES.ORDER]: 'orders-data.ts',
  [TablesTypes.TABLE_NAMES.ORDER_SERVICE]: 'order-service-data.ts',
  [TablesTypes.TABLE_NAMES.CLI_VERSION]: 'cli-version-data.ts',
  [TablesTypes.TABLE_NAMES.CURRENCY]: 'currency-data.ts',
  [TablesTypes.TABLE_NAMES.SERVICE_CATEGORY]: 'service-category-data.ts',
  [TablesTypes.TABLE_NAMES.PROVIDER]: 'provider-data.ts',
  [TablesTypes.TABLE_NAMES.SERVICE_TYPE]: 'service-type-data.ts'
};

// Mock错误定义
const MOCK_ERRORS = {
  NOT_FOUND: { code: 'NOT_FOUND', message: 'Record not found' },
  DUPLICATE_ID: { code: 'DUPLICATE_ID', message: 'ID already exists' },
  VALIDATION_ERROR: { code: 'VALIDATION_ERROR', message: 'Validation failed' }
};

export class MockRPCClient implements RPCTypes.ContractAPI {
  private mockData: Record<string, any[]> = {};
  private isLoaded = false;

  constructor() {
    this.loadMockData();
  }

  // 加载Mock数据
  private async loadMockData() {
    if (this.isLoaded) return;

    try {
      // 动态导入Mock数据
      const userServiceData = (await import('../mock-data/userservice-data')).default;
      const orderData = (await import('../mock-data/orders-data')).default;
      const orderServiceData = (await import('../mock-data/order-service-data')).default;
      const cliVersionData = (await import('../mock-data/cli-version-data')).default;
      const currencyData = (await import('../mock-data/currency-data')).default;
      const serviceCategoryData = (await import('../mock-data/service-category-data')).default;
      const providerData = (await import('../mock-data/provider-data')).default;
      const serviceTypeData = (await import('../mock-data/service-type-data')).default;

      this.mockData = {
        [TablesTypes.TABLE_NAMES.USER_SERVICE]: userServiceData || [],
        [TablesTypes.TABLE_NAMES.ORDER]: orderData || [],
        [TablesTypes.TABLE_NAMES.ORDER_SERVICE]: orderServiceData || [],
        [TablesTypes.TABLE_NAMES.CLI_VERSION]: cliVersionData || [],
        [TablesTypes.TABLE_NAMES.CURRENCY]: currencyData || [],
        [TablesTypes.TABLE_NAMES.SERVICE_CATEGORY]: serviceCategoryData || [],
        [TablesTypes.TABLE_NAMES.PROVIDER]: providerData || [],
        [TablesTypes.TABLE_NAMES.SERVICE_TYPE]: serviceTypeData || []
      };

      this.isLoaded = true;
      console.log('Mock data loaded successfully:', this.mockData);
    } catch (error) {
      console.error('Failed to load mock data:', error);
      // 如果加载失败，使用空数组作为默认值
      this.mockData = {
        [TablesTypes.TABLE_NAMES.USER_SERVICE]: [],
        [TablesTypes.TABLE_NAMES.ORDER]: [],
        [TablesTypes.TABLE_NAMES.ORDER_SERVICE]: [],
        [TablesTypes.TABLE_NAMES.CLI_VERSION]: [],
        [TablesTypes.TABLE_NAMES.CURRENCY]: [],
        [TablesTypes.TABLE_NAMES.SERVICE_CATEGORY]: [],
        [TablesTypes.TABLE_NAMES.PROVIDER]: [],
        [TablesTypes.TABLE_NAMES.SERVICE_TYPE]: []
      };
      this.isLoaded = true;
    }
  }

  // 应用过滤条件
  private applyFilters(data: any[], filter: any): any[] {
    return data.filter(item => {
      // 跳过已删除的记录
      if (item.deletedAt && item.deletedAt > 0) return false;

      // 批量ID查询
      if (filter.ids && filter.ids.length > 0) {
        return filter.ids.includes(item._id);
      }

      // 其他过滤条件
      for (const [key, value] of Object.entries(filter)) {
        if (key === 'offset' || key === 'limit' || key === 'sortBy' || key === 'sortDesc') {
          continue;
        }
        
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            if (!value.includes(item[key])) return false;
          } else if (typeof value === 'string') {
            if (!item[key] || !item[key].toString().toLowerCase().includes(value.toLowerCase())) {
              return false;
            }
          } else {
            if (item[key] !== value) return false;
          }
        }
      }

      return true;
    });
  }

  // 应用排序
  private applySorting(data: any[], sortBy?: string, sortDesc?: boolean): any[] {
    if (!sortBy) return data;

    return [...data].sort((a, b) => {
      const aVal = a[sortBy];
      const bVal = b[sortBy];
      
      let result = 0;
      if (aVal < bVal) result = -1;
      else if (aVal > bVal) result = 1;
      
      return sortDesc ? -result : result;
    });
  }

  // 应用分页
  private applyPagination(data: any[], offset = 0, limit = 100): any[] {
    return data.slice(offset, offset + limit);
  }

  // 获取单条记录
  async get(tableName: string, id: string): Promise<any> {
    await this.loadMockData();
    
    const data = this.mockData[tableName] || [];
    const record = data.find(item => item._id === id && (!item.deletedAt || item.deletedAt === 0));
    
    if (!record) {
      throw new Error(JSON.stringify(MOCK_ERRORS.NOT_FOUND));
    }
    
    return JSON.stringify(record);
  }

  // 查询记录
  async find(tableName: string, filterJson: string): Promise<any> {
    await this.loadMockData();
    
    const filter = JSON.parse(filterJson);
    const data = this.mockData[tableName] || [];

    // 应用过滤条件
    let filtered = this.applyFilters(data, filter);

    // 应用排序
    filtered = this.applySorting(filtered, filter.sortBy, filter.sortDesc);

    // 应用分页
    const offset = filter.offset || 0;
    const limit = filter.limit || 100;
    const paginatedData = this.applyPagination(filtered, offset, limit);

    const result: PaginatedResult<any> = {
      data: paginatedData,
      total: filtered.length,
      offset,
      limit
    };

    return JSON.stringify(result);
  }

  // 计数
  async count(tableName: string, filterJson: string): Promise<number> {
    await this.loadMockData();
    
    const filter = JSON.parse(filterJson);
    const data = this.mockData[tableName] || [];
    const filtered = this.applyFilters(data, filter);
    
    return filtered.length;
  }

  // 插入记录
  async insert(tableName: string, dataJson: string): Promise<string> {
    await this.loadMockData();
    
    const newRecord = JSON.parse(dataJson);
    const data = this.mockData[tableName] || [];
    
    // 检查ID是否已存在
    if (data.some(item => item._id === newRecord._id)) {
      throw new Error(JSON.stringify(MOCK_ERRORS.DUPLICATE_ID));
    }
    
    // 设置时间戳
    const now = Math.floor(Date.now() / 1000);
    newRecord.createdAt = newRecord.createdAt || now;
    newRecord.updatedAt = newRecord.updatedAt || now;
    newRecord.deletedAt = newRecord.deletedAt || 0;
    
    data.push(newRecord);
    return newRecord._id;
  }

  // 批量插入
  async insertMany(tableName: string, dataArrayJson: string): Promise<any> {
    const dataArray = JSON.parse(dataArrayJson);
    const results = [];
    
    for (const record of dataArray) {
      try {
        const id = await this.insert(tableName, JSON.stringify(record));
        results.push({ _id: id, success: true });
      } catch (error) {
        results.push({ _id: record._id, success: false, error: error.message });
      }
    }
    
    return results;
  }

  // 更新记录
  async update(tableName: string, updateJson: string): Promise<void> {
    await this.loadMockData();
    
    const { filter, update_data } = JSON.parse(updateJson);
    const data = this.mockData[tableName] || [];
    
    const recordIndex = data.findIndex(item => 
      this.applyFilters([item], filter).length > 0
    );
    
    if (recordIndex === -1) {
      throw new Error(JSON.stringify(MOCK_ERRORS.NOT_FOUND));
    }
    
    // 更新记录
    const now = Math.floor(Date.now() / 1000);
    Object.assign(data[recordIndex], update_data, { updatedAt: now });
  }

  // 批量更新
  async updateMany(tableName: string, updateManyJson: string): Promise<any> {
    const { filter, update_datas } = JSON.parse(updateManyJson);
    const results = [];
    
    for (const update_data of update_datas) {
      try {
        await this.update(tableName, JSON.stringify({ filter, update_data }));
        results.push({ success: true });
      } catch (error) {
        results.push({ success: false, error: error.message });
      }
    }
    
    return results;
  }

  // 删除记录（软删除）
  async delete(tableName: string, filterJson: string): Promise<void> {
    await this.loadMockData();

    const filter = JSON.parse(filterJson);
    const data = this.mockData[tableName] || [];

    console.log('Delete operation - tableName:', tableName, 'filter:', filter);

    let recordIndex = -1;

    // 如果是通过IDs删除
    if (filter.ids && filter.ids.length > 0) {
      recordIndex = data.findIndex(item => {
        // 处理对象格式的_id
        let itemId = item._id;
        if (typeof itemId === 'object' && itemId?.$oid) {
          itemId = itemId.$oid;
        }
        return filter.ids.includes(itemId) && (!item.deletedAt || item.deletedAt === 0);
      });
    } else {
      // 其他过滤条件
      recordIndex = data.findIndex(item => {
        if (item.deletedAt && item.deletedAt > 0) return false; // 跳过已删除的

        for (const [key, value] of Object.entries(filter)) {
          if (value !== undefined && value !== null && value !== '') {
            if (item[key] !== value) {
              return false;
            }
          }
        }
        return true;
      });
    }

    console.log('Found record index:', recordIndex);

    if (recordIndex === -1) {
      throw new Error(JSON.stringify(MOCK_ERRORS.NOT_FOUND));
    }

    // 软删除
    const now = Math.floor(Date.now() / 1000);
    data[recordIndex].deletedAt = now;
    data[recordIndex].updatedAt = now;

    console.log('Record deleted successfully, deletedAt:', now);
  }

  // 批量删除
  async deleteMany(tableName: string, filterJson: string): Promise<any> {
    await this.loadMockData();
    
    const filter = JSON.parse(filterJson);
    const data = this.mockData[tableName] || [];
    const toDelete = this.applyFilters(data, filter);
    
    const now = Math.floor(Date.now() / 1000);
    let deletedCount = 0;
    
    for (const record of toDelete) {
      const index = data.findIndex(item => item._id === record._id);
      if (index !== -1) {
        data[index].deletedAt = now;
        data[index].updatedAt = now;
        deletedCount++;
      }
    }
    
    return { deletedCount };
  }

  // 批量写入操作
  async bulkWrite(tableName: string, operationsJson: string): Promise<any> {
    const operations = JSON.parse(operationsJson);
    const results = [];
    
    for (const operation of operations) {
      try {
        let result;
        switch (operation.type) {
          case 'insert':
            result = await this.insert(tableName, JSON.stringify(operation.data));
            break;
          case 'update':
            await this.update(tableName, JSON.stringify({
              filter: operation.filter,
              update_data: operation.data
            }));
            result = 'updated';
            break;
          case 'delete_many':
            result = await this.deleteMany(tableName, JSON.stringify(operation.filter));
            break;
          default:
            throw new Error(`Unknown operation type: ${operation.type}`);
        }
        results.push({ success: true, result });
      } catch (error) {
        results.push({ success: false, error: error.message });
      }
    }
    
    return results;
  }
}
