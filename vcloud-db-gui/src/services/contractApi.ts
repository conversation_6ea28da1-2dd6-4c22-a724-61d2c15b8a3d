// 合约API服务封装
import * as RPCTypes from '../types/rpc';
import * as TablesTypes from '../types/tables';
import * as QueryTypes from '../types/query';
import { MockRPCClient } from './mockRpcClient';

export class ContractApiService {
  private client: RPCTypes.ContractAPI;

  constructor(useMock = true) {
    if (useMock) {
      this.client = new MockRPCClient();
    } else {
      // TODO: 实现真实的RPC客户端
      throw new Error('Real RPC client not implemented yet');
    }
  }

  // 获取单条记录
  async getRecord<T extends TablesTypes.TableName>(
    tableName: T,
    id: string
  ): Promise<TablesTypes.TableTypeMap[T]> {
    try {
      const result = await this.client.get(tableName, id);
      return JSON.parse(result);
    } catch (error) {
      console.error(`Failed to get record from ${tableName}:`, error);
      throw error;
    }
  }

  // 查询记录
  async findRecords<T extends TablesTypes.TableName>(
    tableName: T,
    params: QueryTypes.QueryParams = {}
  ): Promise<RPCTypes.PaginatedResult<TablesTypes.TableTypeMap[T]>> {
    try {
      const filterJson = JSON.stringify(params);
      const result = await this.client.find(tableName, filterJson);
      return JSON.parse(result);
    } catch (error) {
      console.error(`Failed to find records from ${tableName}:`, error);
      throw error;
    }
  }

  // 计数
  async countRecords<T extends TablesTypes.TableName>(
    tableName: T,
    params: QueryTypes.QueryParams = {}
  ): Promise<number> {
    try {
      const filterJson = JSON.stringify(params);
      return await this.client.count(tableName, filterJson);
    } catch (error) {
      console.error(`Failed to count records from ${tableName}:`, error);
      throw error;
    }
  }

  // 插入记录
  async insertRecord<T extends TablesTypes.TableName>(
    tableName: T,
    data: Partial<TablesTypes.TableTypeMap[T]>
  ): Promise<string> {
    try {
      // 生成ID如果没有提供
      if (!data._id) {
        data._id = this.generateId();
      }

      // 设置时间戳
      const now = Math.floor(Date.now() / 1000);
      data.createdAt = data.createdAt || now;
      data.updatedAt = data.updatedAt || now;
      data.deletedAt = data.deletedAt || 0;

      const dataJson = JSON.stringify(data);
      return await this.client.insert(tableName, dataJson);
    } catch (error) {
      console.error(`Failed to insert record to ${tableName}:`, error);
      throw error;
    }
  }

  // 批量插入
  async insertManyRecords<T extends TableName>(
    tableName: T,
    dataArray: Partial<TableTypeMap[T]>[]
  ): Promise<any> {
    try {
      // 为每条记录设置默认值
      const processedData = dataArray.map(data => {
        if (!data._id) {
          data._id = this.generateId();
        }
        
        const now = Math.floor(Date.now() / 1000);
        data.createdAt = data.createdAt || now;
        data.updatedAt = data.updatedAt || now;
        data.deletedAt = data.deletedAt || 0;
        
        return data;
      });

      const dataArrayJson = JSON.stringify(processedData);
      return await this.client.insertMany(tableName, dataArrayJson);
    } catch (error) {
      console.error(`Failed to insert many records to ${tableName}:`, error);
      throw error;
    }
  }

  // 更新记录
  async updateRecord<T extends TableName>(
    tableName: T,
    filter: QueryParams,
    updateData: Partial<TableTypeMap[T]>
  ): Promise<void> {
    try {
      // 设置更新时间戳
      const now = Math.floor(Date.now() / 1000);
      updateData.updatedAt = now;

      const updateJson = JSON.stringify({
        filter,
        update_data: updateData
      });
      
      await this.client.update(tableName, updateJson);
    } catch (error) {
      console.error(`Failed to update record in ${tableName}:`, error);
      throw error;
    }
  }

  // 批量更新
  async updateManyRecords<T extends TableName>(
    tableName: T,
    filter: QueryParams,
    updateDataArray: Partial<TableTypeMap[T]>[]
  ): Promise<any> {
    try {
      // 为每条更新数据设置时间戳
      const now = Math.floor(Date.now() / 1000);
      const processedData = updateDataArray.map(data => ({
        ...data,
        updatedAt: now
      }));

      const updateManyJson = JSON.stringify({
        filter,
        update_datas: processedData
      });
      
      return await this.client.updateMany(tableName, updateManyJson);
    } catch (error) {
      console.error(`Failed to update many records in ${tableName}:`, error);
      throw error;
    }
  }

  // 删除记录（软删除）
  async deleteRecord<T extends TableName>(
    tableName: T,
    filter: QueryParams
  ): Promise<void> {
    try {
      const filterJson = JSON.stringify(filter);
      await this.client.delete(tableName, filterJson);
    } catch (error) {
      console.error(`Failed to delete record from ${tableName}:`, error);
      throw error;
    }
  }

  // 批量删除
  async deleteManyRecords<T extends TableName>(
    tableName: T,
    filter: QueryParams
  ): Promise<any> {
    try {
      const filterJson = JSON.stringify(filter);
      return await this.client.deleteMany(tableName, filterJson);
    } catch (error) {
      console.error(`Failed to delete many records from ${tableName}:`, error);
      throw error;
    }
  }

  // 批量写入操作
  async bulkWrite<T extends TableName>(
    tableName: T,
    operations: any[]
  ): Promise<any> {
    try {
      const operationsJson = JSON.stringify(operations);
      return await this.client.bulkWrite(tableName, operationsJson);
    } catch (error) {
      console.error(`Failed to bulk write to ${tableName}:`, error);
      throw error;
    }
  }

  // 生成唯一ID
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 导出单例实例
export const contractApi = new ContractApiService(true); // 使用Mock模式
