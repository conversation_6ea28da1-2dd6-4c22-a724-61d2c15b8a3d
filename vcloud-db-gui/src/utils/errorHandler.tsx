// 错误处理工具类

import React from 'react';
import { message, Modal, notification } from 'antd';
import { ExclamationCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';

export interface ErrorInfo {
  code?: string;
  message: string;
  details?: any;
  timestamp?: number;
}

export class ErrorHandler {
  // 显示错误消息
  static showError(error: ErrorInfo | string, duration: number = 5) {
    const errorMsg = typeof error === 'string' ? error : error.message;
    
    message.error({
      content: errorMsg,
      duration,
      icon: <CloseCircleOutlined />,
    });
  }

  // 显示成功消息
  static showSuccess(msg: string, duration: number = 3) {
    message.success({
      content: msg,
      duration,
      icon: '✅',
    });
  }

  // 显示警告消息
  static showWarning(msg: string, duration: number = 4) {
    message.warning({
      content: msg,
      duration,
      icon: '⚠️',
    });
  }

  // 显示信息消息
  static showInfo(msg: string, duration: number = 3) {
    message.info({
      content: msg,
      duration,
    });
  }

  // 显示详细错误对话框
  static showErrorModal(error: ErrorInfo | string, title: string = '操作失败') {
    const errorInfo = typeof error === 'string' ? { message: error } : error;
    
    Modal.error({
      title,
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p><strong>错误信息：</strong>{errorInfo.message}</p>
          {errorInfo.code && (
            <p><strong>错误代码：</strong>{errorInfo.code}</p>
          )}
          {errorInfo.details && (
            <details style={{ marginTop: '12px' }}>
              <summary style={{ cursor: 'pointer', color: '#666' }}>
                查看详细信息
              </summary>
              <pre style={{ 
                background: '#f5f5f5', 
                padding: '8px', 
                borderRadius: '4px',
                fontSize: '12px',
                marginTop: '8px',
                maxHeight: '200px',
                overflow: 'auto'
              }}>
                {JSON.stringify(errorInfo.details, null, 2)}
              </pre>
            </details>
          )}
        </div>
      ),
      width: 500,
    });
  }

  // 显示确认对话框
  static showConfirm(
    title: string,
    content: string,
    onOk: () => void,
    onCancel?: () => void
  ) {
    Modal.confirm({
      title,
      icon: <ExclamationCircleOutlined />,
      content,
      okText: '确认',
      cancelText: '取消',
      onOk,
      onCancel,
    });
  }

  // 显示通知
  static showNotification(
    type: 'success' | 'info' | 'warning' | 'error',
    title: string,
    description?: string,
    duration: number = 4.5
  ) {
    notification[type]({
      message: title,
      description,
      duration,
      placement: 'topRight',
    });
  }

  // 处理API错误
  static handleApiError(error: any, operation: string = '操作') {
    console.error(`${operation} failed:`, error);
    
    let errorMsg = `${operation}失败`;
    let errorCode = '';
    let errorDetails = null;

    if (error.response) {
      // HTTP错误响应
      errorCode = `HTTP ${error.response.status}`;
      errorMsg = error.response.data?.message || `${operation}失败 (${errorCode})`;
      errorDetails = error.response.data;
    } else if (error.request) {
      // 网络错误
      errorCode = 'NETWORK_ERROR';
      errorMsg = '网络连接失败，请检查网络设置';
      errorDetails = { request: error.request };
    } else if (error.message) {
      // 其他错误
      errorMsg = error.message;
      errorDetails = error;
    }

    const errorInfo: ErrorInfo = {
      code: errorCode,
      message: errorMsg,
      details: errorDetails,
      timestamp: Date.now(),
    };

    // 根据错误类型选择显示方式
    if (errorCode === 'NETWORK_ERROR') {
      this.showErrorModal(errorInfo, '网络连接错误');
    } else if (error.response?.status >= 500) {
      this.showErrorModal(errorInfo, '服务器错误');
    } else {
      this.showError(errorInfo);
    }

    return errorInfo;
  }

  // 处理表单验证错误
  static handleValidationError(errors: any) {
    const errorMessages = Object.values(errors).flat() as string[];
    const errorMsg = errorMessages.join('; ');
    
    this.showWarning(`表单验证失败: ${errorMsg}`);
  }

  // 记录错误日志（可以扩展为发送到服务器）
  static logError(error: ErrorInfo) {
    const logEntry = {
      ...error,
      timestamp: error.timestamp || Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    // 在开发环境下打印到控制台
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Log');
      console.error('Error Info:', logEntry);
      console.groupEnd();
    }

    // 在生产环境下可以发送到错误监控服务
    // 例如：Sentry, LogRocket, 或自定义错误收集服务
  }
}

// 全局错误处理
export const setupGlobalErrorHandler = () => {
  // 捕获未处理的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    ErrorHandler.handleApiError(event.reason, '系统操作');
    event.preventDefault();
  });

  // 捕获JavaScript运行时错误
  window.addEventListener('error', (event) => {
    console.error('JavaScript error:', event.error);
    ErrorHandler.showError('系统发生未知错误，请刷新页面重试');
  });
};

export default ErrorHandler;
