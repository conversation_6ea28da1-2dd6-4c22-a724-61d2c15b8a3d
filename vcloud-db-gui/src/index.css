/* VCloud DB Web GUI 全局样式 */

:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* 主题色彩 */
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --text-color: #262626;
  --text-color-secondary: #8c8c8c;
  --background-color: #f0f2f5;
  --card-background: #ffffff;
  --border-color: #e8e8e8;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: var(--background-color);
  color: var(--text-color);
  font-size: 14px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-color);
  margin: 0;
  font-weight: 600;
}

/* 卡片阴影效果 */
.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  transition: box-shadow 0.3s ease;
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 按钮样式优化 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 表格样式优化 */
.ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background: linear-gradient(90deg, #fafafa 0%, #f5f5f5 100%);
  border-bottom: 2px solid var(--border-color);
  font-weight: 600;
  color: var(--text-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  body {
    font-size: 13px;
  }

  .ant-card {
    margin: 8px;
    border-radius: 6px;
  }

  .ant-btn {
    font-size: 13px;
    padding: 4px 12px;
  }
}
