# VCloud DB Web GUI 项目计划

## 核心目标
- **数据查看**：提供直观的界面查看各种数据表的内容
- **数据修改**：支持对数据进行增删改查操作

## 技术架构

### 前端直连RPC架构
```
Web前端 (React + RPC客户端)
    ↓ JSON-RPC over HTTP
VGraph节点 (Python RPC服务器)
    ↓ 合约调用
VCloud DB合约 (Rust/WASM)
```

### 技术栈
- **框架**：React 18 + TypeScript
- **UI组件库**：Ant Design
- **构建工具**：Vite
- **RPC客户端**：基于fetch API的JSON-RPC实现
- **状态管理**：React useState/useContext

## 项目结构
```
vcloud_db_web_gui/
├── src/
│   ├── components/          # UI组件
│   │   ├── TableSelector/   # 表选择器
│   │   ├── DataTable/       # 数据表格
│   │   ├── DataForm/        # 数据表单
│   │   └── Pagination/      # 分页组件
│   ├── services/            # RPC服务
│   │   ├── rpcClient.ts     # RPC客户端
│   │   └── contractApi.ts   # 合约API封装
│   ├── types/               # TypeScript类型定义
│   │   ├── tables.ts        # 数据表类型
│   │   └── rpc.ts           # RPC类型
│   ├── utils/               # 工具函数
│   ├── App.tsx              # 主应用组件
│   └── main.tsx             # 入口文件
├── public/
├── package.json
├── vite.config.ts
└── tsconfig.json
```

## 开发阶段

### 阶段一：基础框架搭建（1天）✅ **已完成**
**任务清单：**
- [x] 创建Vite + React + TypeScript项目
- [x] 安装和配置Ant Design
- [x] 创建基础项目结构
- [x] 实现Mock RPC客户端
- [x] 创建8个数据表的基础页面结构

**关键文件：**
- `src/services/rpcClient.ts` - RPC客户端基础框架
- `src/types/tables.ts` - 8个数据表的TypeScript类型定义
- `src/App.tsx` - 主应用布局

### 阶段二：数据查看功能（2天）✅ **已完成**
**任务清单：**
- [x] 实现数据表格组件（基于Ant Design Table）
- [x] 实现表切换功能
- [x] 调用合约find函数获取数据
- [x] 实现基础分页功能
- [x] 添加数据加载状态

**关键组件：**
- `TableSelector` - 表选择下拉框
- `DataTable` - 数据展示表格
- `Pagination` - 分页控制器

### 阶段三：数据编辑功能（2天）✅ **已完成**
**任务清单：**
- [x] 实现数据新增表单
- [x] 实现数据编辑表单（Modal形式）
- [x] 实现数据删除功能
- [x] 调用合约insert/update/delete函数
- [x] 添加基础表单验证

**关键组件：**
- `DataForm` - 通用数据表单
- `AddModal` - 新增数据弹窗
- `EditModal` - 编辑数据弹窗

### 阶段四：功能完善（1天）🔄 **进行中**
**任务清单：**
- [/] 错误处理和用户提示
- [ ] 界面优化和响应式设计
- [ ] 基础测试和bug修复
- [ ] 准备生产环境构建

## 需要的外部信息

### VGraph节点连接信息
```typescript
interface NodeConfig {
  rpcUrl: string;           // 节点RPC地址，如: "http://localhost:8545"
  contractAddress: string;  // 合约地址，如: "0x123..."
  timeout?: number;         // 请求超时时间，默认30秒
}
```

### 合约函数调用格式
```typescript
// 需要确认的RPC调用格式
interface RPCCall {
  method: string;           // 如: "contract_call" 或具体的方法名
  params: {
    contract: string;       // 合约地址
    function: string;       // 函数名，如: "find", "insert"
    args: any[];           // 函数参数
  };
}
```

### 数据表结构信息
**详细信息请参考：`data_types_and_mock.md`**

- 8个数据表的完整TypeScript类型定义
- 每个字段的类型和验证规则
- 必填字段和可选字段标识
- 字段的业务含义和示例值
- 现有测试数据文件位置和格式

## Mock数据策略

### 开发阶段使用Mock数据
**Mock数据来源：**
- 使用`contract/base/tests/vcloud-data/`目录下的真实测试数据
- 8个JSON文件对应8个数据表的示例数据
- 数据格式完全符合合约接口要求

**Mock实现策略：**
```typescript
// Mock RPC客户端示例
class MockRPCClient {
  async find(tableName: string, filter: any): Promise<any> {
    const data = await import(`../mock-data/${tableName}-data.json`);
    // 模拟分页和过滤逻辑
    return this.applyFilters(data.default, filter);
  }
}
```

### 真实数据集成
- 替换Mock客户端为真实RPC调用
- 配置节点连接参数
- 处理网络错误和超时
- 保持相同的接口，无需修改UI组件

## 部署方案

### 开发环境
```bash
npm run dev  # Vite开发服务器
```

### 生产环境
```bash
npm run build    # 构建静态文件
npm run preview  # 预览构建结果
```

### 静态文件部署
- 构建产物：`dist/` 目录
- 部署方式：任何静态文件服务器（Nginx、Apache、CDN）
- 配置要求：支持SPA路由（如需要）

## 关键依赖包
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "antd": "^5.12.0",
    "typescript": "^5.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@vitejs/plugin-react": "^4.0.0",
    "vite": "^5.0.0"
  }
}
```

## 注意事项

### CORS配置
- VGraph节点需要配置CORS允许Web前端访问
- 或者使用Nginx反向代理解决跨域问题

### 错误处理
- 网络连接错误
- RPC调用错误
- 数据验证错误
- 用户操作错误

### 性能考虑
- 大数据量的分页处理
- 表格虚拟滚动（如需要）
- 请求防抖和节流

## AI开发助手信息

### 项目上下文
- 这是一个区块链数据库的管理界面
- 数据存储在VGraph区块链节点上
- 通过智能合约进行数据操作
- 用户主要是开发者和管理员

### 开发重点
- 界面简洁实用，不需要过度设计
- 功能完整可靠，重点是数据的准确性
- 错误处理要完善，给用户明确的反馈
- 代码结构清晰，便于后续扩展

### 技术决策原则
- 优先选择成熟稳定的技术栈
- 避免过度工程化，保持简单
- 预留扩展接口，但不提前实现
- 重视用户体验和开发体验
